using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading;
using EasyHook;

namespace CowpilotCaptureHook
{
    public class HookEntryPoint : IEntryPoint
    {
        private HookInterface _interface;
        private LocalHook _sendHook;
        private LocalHook _recvHook;
        private static readonly ConcurrentDictionary<IntPtr, SocketInfo> _sockets =
            new ConcurrentDictionary<IntPtr, SocketInfo>();

        public HookEntryPoint(RemoteHooking.IContext context, string channelName)
        {
            _interface = RemoteHooking.IpcConnectClient<HookInterface>(channelName);
            _interface.Ping();
        }

        public void Run(RemoteHooking.IContext context, string channelName)
        {
            try
            {
                // Install hooks
                _sendHook = LocalHook.Create(
                    LocalHook.GetProcAddress("ws2_32.dll", "send"),
                    new DSend(SendHook),
                    this
                );

                _recvHook = LocalHook.Create(
                    LocalHook.GetProcAddress("ws2_32.dll", "recv"),
                    new DRecv(RecvHook),
                    this
                );

                // Enable hooks for all threads
                _sendHook.ThreadACL.SetExclusiveACL(new Int32[0]);
                _recvHook.ThreadACL.SetExclusiveACL(new Int32[0]);

                _interface.IsInstalled(RemoteHooking.GetCurrentProcessId());

                // Keep the hook alive
                RemoteHooking.WakeUpProcess();

                int cleanupCounter = 0;
                while (true)
                {
                    Thread.Sleep(500);

                    // Clean up inactive sockets every 60 iterations (30 seconds)
                    if (++cleanupCounter >= 60)
                    {
                        CleanupInactiveSockets();
                        cleanupCounter = 0;
                    }

                    if (_interface.ShouldExit())
                        break;
                }
            }
            catch (Exception ex)
            {
                _interface.ReportException(ex);
            }
            finally
            {
                _sendHook?.Dispose();
                _recvHook?.Dispose();
            }
        }

        [UnmanagedFunctionPointer(CallingConvention.StdCall, SetLastError = true)]
        private delegate int DSend(IntPtr socket, IntPtr buffer, int length, int flags);

        [UnmanagedFunctionPointer(CallingConvention.StdCall, SetLastError = true)]
        private delegate int DRecv(IntPtr socket, IntPtr buffer, int length, int flags);

        private int SendHook(IntPtr socket, IntPtr buffer, int length, int flags)
        {
            try
            {
                // Store socket handle for later use
                _sockets.TryAdd(
                    socket,
                    new SocketInfo { Handle = socket, LastActivity = DateTime.Now }
                );

                // Read packet data
                byte[] data = new byte[length];
                Marshal.Copy(buffer, data, 0, length);

                _interface.OnPacketSent(data);
            }
            catch (Exception ex)
            {
                _interface.ReportException(ex);
            }

            // Call original function
            return send(socket, buffer, length, flags);
        }

        private int RecvHook(IntPtr socket, IntPtr buffer, int length, int flags)
        {
            int result = recv(socket, buffer, length, flags);

            try
            {
                if (result > 0)
                {
                    // Get or create socket info
                    var socketInfo = _sockets.GetOrAdd(
                        socket,
                        _ => new SocketInfo { Handle = socket, LastActivity = DateTime.Now }
                    );
                    socketInfo.LastActivity = DateTime.Now;

                    // Read received data
                    byte[] data = new byte[result];
                    Marshal.Copy(buffer, data, 0, result);

                    // Add data to the socket's receive buffer
                    socketInfo.ReceiveBuffer.AddRange(data);

                    // Prevent buffer from growing too large (max 1MB)
                    if (socketInfo.ReceiveBuffer.Count > 1048576)
                    {
                        // Clear buffer if it gets too large - this indicates a problem
                        socketInfo.ReceiveBuffer.Clear();
                        _interface.ReportException(
                            new Exception(
                                "Receive buffer exceeded 1MB, cleared to prevent memory leak"
                            )
                        );
                        return result;
                    }

                    // Extract complete packets from the buffer
                    var packets = PacketParser.ExtractPackets(socketInfo.ReceiveBuffer);

                    // Process each complete packet
                    foreach (var packet in packets)
                    {
                        _interface.OnPacketReceived(packet);
                    }
                }
            }
            catch (Exception ex)
            {
                _interface.ReportException(ex);
            }

            return result;
        }

        [DllImport("ws2_32.dll", SetLastError = true)]
        private static extern int send(IntPtr socket, IntPtr buffer, int length, int flags);

        [DllImport("ws2_32.dll", SetLastError = true)]
        private static extern int recv(IntPtr socket, IntPtr buffer, int length, int flags);

        // Static method to send custom packets from IPC
        public static int SendCustomPacket(byte[] data)
        {
            try
            {
                // Get the most recently active socket
                var activeSocket = GetMostActiveSocket();
                if (activeSocket == IntPtr.Zero)
                {
                    throw new InvalidOperationException("No active socket found");
                }

                // Allocate unmanaged memory for the packet data
                IntPtr buffer = Marshal.AllocHGlobal(data.Length);
                try
                {
                    Marshal.Copy(data, 0, buffer, data.Length);
                    return send(activeSocket, buffer, data.Length, 0);
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception)
            {
                return -1; // SOCKET_ERROR
            }
        }

        private static IntPtr GetMostActiveSocket()
        {
            IntPtr mostActive = IntPtr.Zero;
            DateTime latestActivity = DateTime.MinValue;

            foreach (var kvp in _sockets)
            {
                if (kvp.Value.LastActivity > latestActivity)
                {
                    latestActivity = kvp.Value.LastActivity;
                    mostActive = kvp.Key;
                }
            }

            return mostActive;
        }

        /// <summary>
        /// Cleans up socket buffers for sockets that haven't been active for more than the specified timeout.
        /// This prevents memory leaks from accumulating receive buffers.
        /// </summary>
        /// <param name="timeoutMinutes">Timeout in minutes for inactive sockets</param>
        private static void CleanupInactiveSockets(int timeoutMinutes = 5)
        {
            var cutoffTime = DateTime.Now.AddMinutes(-timeoutMinutes);
            var socketsToRemove = new List<IntPtr>();

            foreach (var kvp in _sockets)
            {
                if (kvp.Value.LastActivity < cutoffTime)
                {
                    socketsToRemove.Add(kvp.Key);
                }
            }

            foreach (var socket in socketsToRemove)
            {
                _sockets.TryRemove(socket, out _);
            }
        }
    }
}
