using System;

namespace CowpilotCapture.Encryption
{
    /// <summary>
    /// Main class for Mu Online packet encryption and decryption.
    /// Handles both client-to-server and server-to-client packet processing.
    /// </summary>
    public class PacketCrypto
    {
        private readonly SimpleModulusCrypto _serverEncryptor;
        private readonly SimpleModulusCrypto _serverDecryptor;
        private readonly SimpleModulusCrypto _clientEncryptor;
        private readonly SimpleModulusCrypto _clientDecryptor;

        /// <summary>
        /// Initializes a new instance of the <see cref="PacketCrypto"/> class with default keys.
        /// </summary>
        public PacketCrypto()
        {
            _serverEncryptor = new SimpleModulusCrypto(
                SimpleModulusCrypto.DefaultServerEncryptionKeys,
                true
            );
            _serverDecryptor = new SimpleModulusCrypto(
                SimpleModulusCrypto.DefaultServerDecryptionKeys,
                false
            );
            _clientEncryptor = new SimpleModulusCrypto(
                SimpleModulusCrypto.DefaultClientEncryptionKeys,
                true
            );
            _clientDecryptor = new SimpleModulusCrypto(
                SimpleModulusCrypto.DefaultClientDecryptionKeys,
                false
            );
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="PacketCrypto"/> class with custom keys.
        /// </summary>
        /// <param name="serverEncryptionKeys">Server encryption keys.</param>
        /// <param name="serverDecryptionKeys">Server decryption keys.</param>
        /// <param name="clientEncryptionKeys">Client encryption keys.</param>
        /// <param name="clientDecryptionKeys">Client decryption keys.</param>
        public PacketCrypto(
            SimpleModulusKeys serverEncryptionKeys,
            SimpleModulusKeys serverDecryptionKeys,
            SimpleModulusKeys clientEncryptionKeys,
            SimpleModulusKeys clientDecryptionKeys
        )
        {
            _serverEncryptor = new SimpleModulusCrypto(serverEncryptionKeys, true);
            _serverDecryptor = new SimpleModulusCrypto(serverDecryptionKeys, false);
            _clientEncryptor = new SimpleModulusCrypto(clientEncryptionKeys, true);
            _clientDecryptor = new SimpleModulusCrypto(clientDecryptionKeys, false);
        }

        /// <summary>
        /// Gets the server encryptor counter.
        /// </summary>
        public Counter ServerEncryptorCounter => _serverEncryptor.Counter;

        /// <summary>
        /// Gets the server decryptor counter.
        /// </summary>
        public Counter ServerDecryptorCounter => _serverDecryptor.Counter;

        /// <summary>
        /// Gets the client encryptor counter.
        /// </summary>
        public Counter ClientEncryptorCounter => _clientEncryptor.Counter;

        /// <summary>
        /// Gets the client decryptor counter.
        /// </summary>
        public Counter ClientDecryptorCounter => _clientDecryptor.Counter;

        /// <summary>
        /// Encrypts a packet going from server to client.
        /// C1/C2 packets: Only XOR32 encryption
        /// C3/C4 packets: SimpleModulus + XOR32 encryption
        /// </summary>
        /// <param name="packet">The packet to encrypt.</param>
        /// <param name="xor32Key">The XOR32 key. If null, uses the default Pre-Season 6 key.</param>
        /// <returns>The encrypted packet.</returns>
        public byte[] EncryptServerToClient(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (packet.IsEncrypted())
            {
                // No XOR32 encryption for server
                //var xorEncrypted = Xor32Crypto.Encrypt(packet, xor32Key);
                return _serverEncryptor.Encrypt(packet);
            }
            else if (packet.IsUnencrypted())
            {
                return packet;
                // No XOR32 encryption for Server -> Client
                //return Xor32Crypto.Encrypt(packet, xor32Key);
            }
            else
            {
                throw new ArgumentException($"Unknown packet type: {packet[0]:X2}", nameof(packet));
            }
        }

        /// <summary>
        /// Decrypts a packet coming from client to server.
        /// C1/C2 packets: Only XOR32 decryption
        /// C3/C4 packets: XOR32 FIRST, then SimpleModulus decryption (reverse order of encryption)
        /// </summary>
        /// <param name="packet">The packet to decrypt.</param>
        /// <param name="xor32Key">The XOR32 key. If null, uses the default Pre-Season 6 key.</param>
        /// <returns>The decrypted packet.</returns>
        public byte[] DecryptClientToServer(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (packet.IsEncrypted())
            {
                // C3/C4 packets: SimpleModulus FIRST, then XOR32 (reverse order of encryption)
                var simpleModulusDecrypted = _serverDecryptor.Decrypt(packet);
                return Xor32Crypto.Decrypt(simpleModulusDecrypted, xor32Key);
            }
            else if (packet.IsUnencrypted())
            {
                // C1/C2 packets: Only XOR32
                return Xor32Crypto.Decrypt(packet, xor32Key);
            }
            else
            {
                throw new ArgumentException($"Unknown packet type: {packet[0]:X2}", nameof(packet));
            }
        }

        /// <summary>
        /// Encrypts a packet going from client to server.
        /// C1/C2 packets: Only XOR32 encryption
        /// C3/C4 packets: SimpleModulus FIRST, then XOR32 encryption
        /// </summary>
        /// <param name="packet">The packet to encrypt.</param>
        /// <param name="xor32Key">The XOR32 key. If null, uses the default Pre-Season 6 key.</param>
        /// <returns>The encrypted packet.</returns>
        public byte[] EncryptClientToServer(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (packet.IsEncrypted())
            {
                // C3/C4 packets: XOR32 FIRST, then SimpleModulus
                var xorEncrypted = Xor32Crypto.Encrypt(packet, xor32Key);
                return _clientEncryptor.Encrypt(xorEncrypted);
            }
            else if (packet.IsUnencrypted())
            {
                // C1/C2 packets: Only XOR32
                return Xor32Crypto.Encrypt(packet, xor32Key);
            }
            else
            {
                throw new ArgumentException($"Unknown packet type: {packet[0]:X2}", nameof(packet));
            }
        }

        /// <summary>
        /// Decrypts a packet coming from server to client.
        /// C1/C2 packets: Only XOR32 decryption
        /// C3/C4 packets: XOR32 FIRST, then SimpleModulus decryption (reverse order of encryption)
        /// </summary>
        /// <param name="packet">The packet to decrypt.</param>
        /// <param name="xor32Key">The XOR32 key. If null, uses the default Pre-Season 6 key.</param>
        /// <returns>The decrypted packet.</returns>
        public byte[] DecryptServerToClient(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (packet.IsEncrypted())
            {
                var simpleModulusDecrypted = _clientDecryptor.Decrypt(packet);
                return simpleModulusDecrypted;
                // No XOR32 encryption for Server -> Client
                //return Xor32Crypto.Decrypt(simpleModulusDecrypted, xor32Key);
            }
            else if (packet.IsUnencrypted())
            {
                return packet;
                // No XOR32 encryption for Server -> Client
                // C1/C2 packets: Only XOR32
                //return Xor32Crypto.Decrypt(packet, xor32Key);
            }
            else
            {
                throw new ArgumentException($"Unknown packet type: {packet[0]:X2}", nameof(packet));
            }
        }

        /// <summary>
        /// Resets all counters to their initial state.
        /// </summary>
        public void ResetCounters()
        {
            _serverEncryptor.Counter.Reset();
            _serverDecryptor.Counter.Reset();
            _clientEncryptor.Counter.Reset();
            _clientDecryptor.Counter.Reset();
        }

        /// <summary>
        /// Creates a packet crypto instance for server-side operations.
        /// </summary>
        /// <returns>A PacketCrypto instance configured for server operations.</returns>
        public static PacketCrypto CreateForServer()
        {
            return new PacketCrypto();
        }

        /// <summary>
        /// Creates a packet crypto instance for client-side operations.
        /// </summary>
        /// <returns>A PacketCrypto instance configured for client operations.</returns>
        public static PacketCrypto CreateForClient()
        {
            return new PacketCrypto();
        }
    }
}
