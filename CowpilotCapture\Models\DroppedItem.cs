﻿using System;
using System.Linq;
using System.Xml.Schema;

namespace CowpilotCapture.Models
{
    public class DroppedItem
    {
        private int id;
        private bool isFreshDrop;
        private int positionX;
        private int positionY;
        private ItemData itemData;

        public static int Length => 16;
        public int Id => id;
        public bool IsFreshDrop => isFreshDrop;
        public int PositionX => positionX;
        public int PositionY => positionY;
        public ItemData ItemData => itemData;

        public DroppedItem(byte[] data)
        {
            this.id = data[0] << 8 | data[1];
            this.isFreshDrop = Convert.ToBoolean(data[0] << 7);
            this.positionX = data[2];
            this.positionY = data[3];
            this.itemData = new ItemData(data.Skip(4).ToArray());
        }
    }
}
