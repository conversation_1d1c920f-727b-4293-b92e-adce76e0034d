﻿namespace CowpilotCapture.Models
{
    public class ItemData
    {
        private readonly int index;
        private readonly int group;
        private readonly int id;
        private readonly int level;

        public int Index => index;
        public int Group => group;
        public int Id => id;
        public int Level => level;

        public ItemData(byte[] data)
        {
            this.index = data[0] + ((data[0] & 0x80) << 1);
            this.group = (data[5] & 0xF0) >> 4;
            this.level = (data[1] & 0x78) >> 3;

            this.id = (this.group << 9) + this.index;
        }
    }
}
