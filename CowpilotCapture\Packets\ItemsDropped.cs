﻿using System.Collections.Generic;
using System.Linq;
using CowpilotCapture.Models;

namespace CowpilotCapture.Packets
{
    public class ItemsDropped : Packet
    {
        private List<DroppedItem> items = new List<DroppedItem>();

        public IReadOnlyList<DroppedItem> Items => items;

        public ItemsDropped(byte[] data)
            : base(data)
        {
            var itemCount = data[4];
            var headerSize = 5;

            for (int i = 0; i < itemCount; i++)
            {
                var itemStart = headerSize + (i * DroppedItem.Length);
                var droppedItem = new DroppedItem(
                    data.Skip(itemStart).Take(DroppedItem.Length).ToArray()
                );

                this.items.Add(droppedItem);
            }
        }
    }
}
