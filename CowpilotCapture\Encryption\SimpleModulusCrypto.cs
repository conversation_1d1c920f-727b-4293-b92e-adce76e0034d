using System;

namespace CowpilotCapture.Encryption
{
    /// <summary>
    /// Provides SimpleModulus encryption and decryption functionality for Mu Online C3/C4 packets.
    /// </summary>
    public class SimpleModulusCrypto
    {
        /// <summary>
        /// The xor key which is used to 'encrypt' the size of each block.
        /// </summary>
        private const byte BlockSizeXorKey = 0x3D;

        /// <summary>
        /// The xor key which is used to 'encrypt' the checksum of each encrypted block.
        /// </summary>
        private const byte BlockCheckSumXorKey = 0xF8;

        private const int BitsPerByte = 8;
        private const int BitsPerValue = (BitsPerByte * 2) + 2;

        // For Pre-Season 6 (newer variant)
        private const int DecryptedBlockSize = 8;
        private const int EncryptedBlockSize = 11;
        private const int EncryptionResultLength = 4;

        /// <summary>
        /// Default server side decryption keys for Pre-Season 6.
        /// </summary>
        public static readonly SimpleModulusKeys DefaultServerDecryptionKeys =
            SimpleModulusKeys.CreateDecryptionKeys(
                new uint[]
                {
                    128079,
                    164742,
                    70235,
                    106898,
                    31544,
                    2047,
                    57011,
                    10183,
                    48413,
                    46165,
                    15171,
                    37433,
                }
            );

        /// <summary>
        /// Default client side decryption keys for Pre-Season 6.
        /// </summary>
        public static readonly SimpleModulusKeys DefaultClientDecryptionKeys =
            SimpleModulusKeys.CreateDecryptionKeys(
                new uint[]
                {
                    73326,
                    109989,
                    98843,
                    171058,
                    18035,
                    30340,
                    24701,
                    11141,
                    62004,
                    64409,
                    35374,
                    64599,
                }
            );

        /// <summary>
        /// Default server side encryption keys for Pre-Season 6.
        /// </summary>
        public static readonly SimpleModulusKeys DefaultServerEncryptionKeys =
            SimpleModulusKeys.CreateEncryptionKeys(
                new uint[]
                {
                    73326,
                    109989,
                    98843,
                    171058,
                    13169,
                    19036,
                    35482,
                    29587,
                    62004,
                    64409,
                    35374,
                    64599,
                }
            );

        /// <summary>
        /// Default client side encryption keys for Pre-Season 6.
        /// </summary>
        public static readonly SimpleModulusKeys DefaultClientEncryptionKeys =
            SimpleModulusKeys.CreateEncryptionKeys(
                new uint[]
                {
                    128079,
                    164742,
                    70235,
                    106898,
                    23489,
                    11911,
                    19816,
                    13647,
                    48413,
                    46165,
                    15171,
                    37433,
                }
            );

        private readonly SimpleModulusKeys _keys;
        private readonly bool _isEncryption;
        private readonly Counter _counter;
        private readonly uint[] _encryptionResult;
        private readonly byte[] _inputBuffer;

        /// <summary>
        /// Initializes a new instance of the <see cref="SimpleModulusCrypto"/> class.
        /// </summary>
        /// <param name="keys">The encryption/decryption keys.</param>
        /// <param name="isEncryption">True for encryption, false for decryption.</param>
        public SimpleModulusCrypto(SimpleModulusKeys keys, bool isEncryption)
        {
            _keys = keys ?? throw new ArgumentNullException(nameof(keys));
            _isEncryption = isEncryption;
            _counter = new Counter();
            _encryptionResult = new uint[EncryptionResultLength];
            // Use the appropriate buffer size based on operation
            _inputBuffer = new byte[isEncryption ? DecryptedBlockSize : EncryptedBlockSize];
        }

        /// <summary>
        /// Gets the counter for packet sequencing.
        /// </summary>
        public Counter Counter => _counter;

        /// <summary>
        /// Encrypts a packet using SimpleModulus encryption.
        /// </summary>
        /// <param name="packet">The packet to encrypt.</param>
        /// <returns>The encrypted packet.</returns>
        public byte[] Encrypt(byte[] packet)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (!_isEncryption)
                throw new InvalidOperationException(
                    "This instance is configured for decryption, not encryption."
                );

            var headerSize = packet.GetPacketHeaderSize();
            var contentSize = packet.Length - headerSize + 1; // +1 for counter
            var encryptedSize = GetEncryptedSize(contentSize) + headerSize;
            var result = new byte[encryptedSize];

            // Copy header
            result[0] = packet[0];
            result.SetPacketSize();

            // Encrypt content
            EncryptPacketContent(packet, headerSize, result, headerSize);

            return result;
        }

        /// <summary>
        /// Decrypts a packet using SimpleModulus decryption.
        /// </summary>
        /// <param name="packet">The packet to decrypt.</param>
        /// <returns>The decrypted packet.</returns>
        public byte[] Decrypt(byte[] packet)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (_isEncryption)
                throw new InvalidOperationException(
                    "This instance is configured for encryption, not decryption."
                );

            var headerSize = packet.GetPacketHeaderSize();
            var contentSize = packet.Length - headerSize;

            if ((contentSize % EncryptedBlockSize) != 0)
                throw new ArgumentException(
                    $"The packet has an unexpected content size. It must be a multiple of {EncryptedBlockSize}"
                );

            var maxDecryptedSize = GetMaximumDecryptedSize(packet);
            var result = new byte[maxDecryptedSize];

            // Copy header
            result[0] = packet[0];

            // Decrypt content
            var actualSize = DecryptPacketContent(packet, headerSize, result, headerSize - 1); // -1 because we don't forward the counter
            Array.Resize(ref result, actualSize + headerSize - 1);
            result.SetPacketSize();

            return result;
        }

        private void EncryptPacketContent(
            byte[] input,
            int inputOffset,
            byte[] output,
            int outputOffset
        )
        {
            var sourceOffset = inputOffset;
            var resultOffset = outputOffset;
            var totalDecryptedSize = input.Length - inputOffset;

            // Process first block with counter
            Array.Clear(_inputBuffer, 0, _inputBuffer.Length);
            _inputBuffer[0] = (byte)_counter.Count;

            if (totalDecryptedSize >= DecryptedBlockSize - 1)
            {
                Array.Copy(input, sourceOffset, _inputBuffer, 1, DecryptedBlockSize - 1);
            }
            else
            {
                Array.Copy(input, sourceOffset, _inputBuffer, 1, totalDecryptedSize);
            }

            var contentOfFirstBlockLength = Math.Min(DecryptedBlockSize, totalDecryptedSize + 1);
            EncryptBlock(output, resultOffset, contentOfFirstBlockLength);
            sourceOffset += DecryptedBlockSize - 1;
            resultOffset += EncryptedBlockSize;

            // Process remaining blocks
            while (sourceOffset < input.Length)
            {
                Array.Clear(_inputBuffer, 0, _inputBuffer.Length);
                var contentOfBlockLength = Math.Min(
                    DecryptedBlockSize,
                    input.Length - sourceOffset
                );
                Array.Copy(input, sourceOffset, _inputBuffer, 0, contentOfBlockLength);

                EncryptBlock(output, resultOffset, contentOfBlockLength);
                sourceOffset += DecryptedBlockSize;
                resultOffset += EncryptedBlockSize;
            }

            _counter.Increase();
        }

        private int DecryptPacketContent(
            byte[] input,
            int inputOffset,
            byte[] output,
            int outputOffset
        )
        {
            int sizeCounter = 0;
            var sourceOffset = inputOffset;

            while (sourceOffset < input.Length)
            {
                // Clear the input buffer first
                Array.Clear(_inputBuffer, 0, _inputBuffer.Length);

                // Calculate how many bytes we can actually copy
                var bytesToCopy = Math.Min(EncryptedBlockSize, input.Length - sourceOffset);
                Array.Copy(input, sourceOffset, _inputBuffer, 0, bytesToCopy);

                var blockSize = DecryptBlock(output, outputOffset + sizeCounter);

                if (sizeCounter == 0 && output[outputOffset] != (byte)_counter.Count)
                {
                    throw new InvalidOperationException(
                        $"Invalid packet counter. Expected: {_counter.Count}, Actual: {output[outputOffset]}"
                    );
                }

                if (blockSize != -1)
                {
                    sizeCounter += blockSize;
                }

                sourceOffset += EncryptedBlockSize;
            }

            _counter.Increase();
            return sizeCounter;
        }

        private void EncryptBlock(byte[] outputBuffer, int outputOffset, int blockSize)
        {
            // Clear output block
            for (int i = 0; i < EncryptedBlockSize; i++)
            {
                outputBuffer[outputOffset + i] = 0;
            }

            EncryptContent();

            for (int i = 0; i < _encryptionResult.Length; i++)
            {
                WriteResultToTarget(outputBuffer, outputOffset, i, _encryptionResult[i]);
            }

            EncryptFinalBlockByte(blockSize, outputBuffer, outputOffset);
        }

        private int DecryptBlock(byte[] outputBuffer, int outputOffset)
        {
            for (int i = 0; i < _encryptionResult.Length; i++)
            {
                _encryptionResult[i] = ReadInputBuffer(i);
            }

            DecryptContent(outputBuffer, outputOffset);
            return DecodeFinal(outputBuffer, outputOffset);
        }

        private void EncryptContent()
        {
            var keys = _keys;
            var input = new ushort[DecryptedBlockSize / 2];

            for (int i = 0; i < input.Length; i++)
            {
                input[i] = (ushort)(_inputBuffer[i * 2] | (_inputBuffer[i * 2 + 1] << 8));
            }

            _encryptionResult[0] =
                ((keys.XorKey[0] ^ input[0]) * keys.EncryptKey[0]) % keys.ModulusKey[0];
            for (int i = 1; i < _encryptionResult.Length; i++)
            {
                _encryptionResult[i] =
                    (
                        (keys.XorKey[i] ^ (input[i] ^ (_encryptionResult[i - 1] & 0xFFFF)))
                        * keys.EncryptKey[i]
                    ) % keys.ModulusKey[i];
            }

            for (int i = 0; i < _encryptionResult.Length - 1; i++)
            {
                _encryptionResult[i] =
                    _encryptionResult[i] ^ keys.XorKey[i] ^ (_encryptionResult[i + 1] & 0xFFFF);
            }
        }

        private void DecryptContent(byte[] outputBuffer, int outputOffset)
        {
            var keys = _keys;
            for (int i = _encryptionResult.Length - 1; i > 0; i--)
            {
                _encryptionResult[i - 1] =
                    _encryptionResult[i - 1] ^ keys.XorKey[i - 1] ^ (_encryptionResult[i] & 0xFFFF);
            }

            for (int i = 0; i < _encryptionResult.Length; i++)
            {
                uint result =
                    keys.XorKey[i]
                    ^ ((_encryptionResult[i] * keys.DecryptKey[i]) % keys.ModulusKey[i]);
                if (i > 0)
                {
                    result ^= _encryptionResult[i - 1] & 0xFFFF;
                }

                outputBuffer[outputOffset + i * 2] = (byte)(result & 0xFF);
                outputBuffer[outputOffset + i * 2 + 1] = (byte)((result >> 8) & 0xFF);
            }
        }

        private void EncryptFinalBlockByte(int blockSize, byte[] outputBuffer, int outputOffset)
        {
            byte size = (byte)(blockSize ^ BlockSizeXorKey);
            byte checksum = BlockCheckSumXorKey;
            for (var i = 0; i < blockSize; i++)
            {
                checksum ^= _inputBuffer[i];
            }

            size ^= checksum;
            outputBuffer[outputOffset + EncryptedBlockSize - 2] = size;
            outputBuffer[outputOffset + EncryptedBlockSize - 1] = checksum;
        }

        private int DecodeFinal(byte[] outputBuffer, int outputOffset)
        {
            var blockSuffix = new byte[2];
            var inputBufferSize = _inputBuffer.Length;
            Array.Copy(_inputBuffer, inputBufferSize - 2, blockSuffix, 0, 2);

            byte blockSize = (byte)(blockSuffix[0] ^ blockSuffix[1] ^ BlockSizeXorKey);

            if (blockSize > DecryptedBlockSize)
            {
                throw new InvalidOperationException(
                    $"Invalid block size: {blockSize}. Maximum allowed: {DecryptedBlockSize}"
                );
            }

            byte checksum = BlockCheckSumXorKey;
            for (int i = 0; i < DecryptedBlockSize; i++)
            {
                checksum ^= outputBuffer[outputOffset + i];
            }

            if (blockSuffix[1] != checksum)
            {
                throw new InvalidOperationException(
                    $"Block checksum invalid. Expected: {checksum}. Actual: {blockSuffix[1]}"
                );
            }

            return blockSize;
        }

        private uint ReadInputBuffer(int resultIndex)
        {
            var byteOffset = GetByteOffset(resultIndex);
            var bitOffset = GetBitOffset(resultIndex);
            var firstMask = GetFirstBitMask(resultIndex);
            uint result = 0;

            // Ensure we don't read beyond buffer bounds
            if (byteOffset < _inputBuffer.Length)
                result += (uint)((_inputBuffer[byteOffset++] & firstMask) << (24 + bitOffset));
            if (byteOffset < _inputBuffer.Length)
                result += (uint)(_inputBuffer[byteOffset++] << (16 + bitOffset));
            if (byteOffset < _inputBuffer.Length)
                result += (uint)(
                    (_inputBuffer[byteOffset] & (0xFF << (8 - bitOffset))) << (8 + bitOffset)
                );

            result = ReverseEndianness(result);
            var remainderMask = GetRemainderBitMask(resultIndex);
            if (byteOffset < _inputBuffer.Length)
            {
                var remainder = (byte)(_inputBuffer[byteOffset] & remainderMask);
                result += (uint)(remainder << 16) >> (6 - bitOffset);
            }

            return result;
        }

        private static void WriteResultToTarget(
            byte[] target,
            int targetOffset,
            int resultIndex,
            uint result
        )
        {
            var byteOffset = GetByteOffset(resultIndex);
            var bitOffset = GetBitOffset(resultIndex);
            var firstMask = GetFirstBitMask(resultIndex);
            var swapped = ReverseEndianness(result);
            target[targetOffset + byteOffset++] |= (byte)(swapped >> (24 + bitOffset) & firstMask);
            target[targetOffset + byteOffset++] = (byte)(swapped >> (16 + bitOffset));
            target[targetOffset + byteOffset] = (byte)(
                (swapped >> (8 + bitOffset)) & (0xFF << (8 - bitOffset))
            );
            var remainderMask = GetRemainderBitMask(resultIndex);
            var remainder = (result >> 16) << (6 - bitOffset);
            target[targetOffset + byteOffset] |= (byte)(remainder & remainderMask);
        }

        private static int GetByteOffset(int resultIndex) => GetBitIndex(resultIndex) / BitsPerByte;

        private static int GetBitOffset(int resultIndex) => GetBitIndex(resultIndex) % BitsPerByte;

        private static int GetFirstBitMask(int resultIndex) => 0xFF >> GetBitOffset(resultIndex);

        private static int GetRemainderBitMask(int resultIndex) =>
            (0xFF << (6 - GetBitOffset(resultIndex)) & 0xFF)
            - ((0xFF << (8 - GetBitOffset(resultIndex))) & 0xFF);

        private static int GetBitIndex(int resultIndex) => resultIndex * BitsPerValue;

        private static uint ReverseEndianness(uint value)
        {
            return ((value & 0x000000FF) << 24)
                | ((value & 0x0000FF00) << 8)
                | ((value & 0x00FF0000) >> 8)
                | ((value & 0xFF000000) >> 24);
        }

        private static int GetEncryptedSize(int contentSize)
        {
            return (
                (
                    (contentSize / DecryptedBlockSize)
                    + (((contentSize % DecryptedBlockSize) > 0) ? 1 : 0)
                ) * EncryptedBlockSize
            );
        }

        private static int GetMaximumDecryptedSize(byte[] packet)
        {
            int contentSize = packet.Length - packet.GetPacketHeaderSize();
            contentSize--; // subtract counter
            return (contentSize * DecryptedBlockSize / EncryptedBlockSize)
                + packet.GetPacketHeaderSize();
        }
    }
}
