using System;

namespace CowpilotCapture.Encryption
{
    /// <summary>
    /// Extension methods for packet handling and size calculations.
    /// </summary>
    public static class PacketExtensions
    {
        /// <summary>
        /// Gets the size of a packet from its header.
        /// C1 and C3 packets have a maximum length of 255, and the length defined in the second byte.
        /// C2 and C4 packets have a maximum length of 65535, and the length defined in the second and third byte.
        /// </summary>
        /// <param name="packet">The packet.</param>
        /// <returns>The size of a packet.</returns>
        public static int GetPacketSize(this byte[] packet)
        {
            if (packet == null || packet.Length < 2)
                return 0;

            switch (packet[0])
            {
                case 0xC1:
                case 0xC3:
                    return packet[1];
                case 0xC2:
                case 0xC4:
                    if (packet.Length < 3)
                        return 0;
                    return (packet[1] << 8) | packet[2];
                default:
                    return 0;
            }
        }

        /// <summary>
        /// Gets the header size of a packet.
        /// C1 and C3 packets have 2 bytes header (type + size).
        /// C2 and C4 packets have 3 bytes header (type + size high + size low).
        /// </summary>
        /// <param name="packet">The packet.</param>
        /// <returns>The header size.</returns>
        public static int GetPacketHeaderSize(this byte[] packet)
        {
            if (packet == null || packet.Length == 0)
                return 0;

            switch (packet[0])
            {
                case 0xC1:
                case 0xC3:
                    return 2;
                case 0xC2:
                case 0xC4:
                    return 3;
                default:
                    return 0;
            }
        }

        /// <summary>
        /// Sets the size of the byte array as packet length in the corresponding indexes of the byte array.
        /// </summary>
        /// <param name="packet">The packet.</param>
        public static void SetPacketSize(this byte[] packet)
        {
            if (packet == null || packet.Length == 0)
                return;

            var size = packet.Length;
            switch (packet[0])
            {
                case 0xC1:
                case 0xC3:
                    packet[1] = (byte)size;
                    break;
                case 0xC2:
                case 0xC4:
                    packet[1] = (byte)((size & 0xFF00) >> 8);
                    packet[2] = (byte)(size & 0x00FF);
                    break;
                default:
                    throw new ArgumentException(
                        $"Unknown packet type {packet[0]:X}",
                        nameof(packet)
                    );
            }
        }

        /// <summary>
        /// Determines whether the packet is encrypted (C3 or C4).
        /// </summary>
        /// <param name="packet">The packet.</param>
        /// <returns>True if the packet is encrypted, false otherwise.</returns>
        public static bool IsEncrypted(this byte[] packet)
        {
            if (packet == null || packet.Length == 0)
                return false;

            return packet[0] == 0xC3 || packet[0] == 0xC4;
        }

        /// <summary>
        /// Determines whether the packet is unencrypted (C1 or C2).
        /// </summary>
        /// <param name="packet">The packet.</param>
        /// <returns>True if the packet is unencrypted, false otherwise.</returns>
        public static bool IsUnencrypted(this byte[] packet)
        {
            if (packet == null || packet.Length == 0)
                return false;

            return packet[0] == 0xC1 || packet[0] == 0xC2;
        }
    }
}
