using System;
using System.Collections.Generic;

namespace CowpilotCaptureHook
{
    /// <summary>
    /// Utility class for parsing individual packets from a stream of bytes.
    /// Handles the case where multiple packets are consolidated into a single receive operation.
    /// </summary>
    public static class PacketParser
    {
        /// <summary>
        /// Extracts complete packets from a buffer and returns them.
        /// The buffer is modified to remove the extracted packets.
        /// </summary>
        /// <param name="buffer">The buffer containing received data</param>
        /// <returns>List of complete packets found in the buffer</returns>
        public static List<byte[]> ExtractPackets(List<byte> buffer)
        {
            var packets = new List<byte[]>();

            while (buffer.Count > 0)
            {
                // Check if we have enough bytes to determine packet type and size
                if (buffer.Count < 2)
                    break; // Need at least 2 bytes for C1/C3 packets

                var packetType = buffer[0];
                int packetSize = GetPacketSize(buffer);

                if (packetSize == 0)
                {
                    // Invalid packet type, remove the first byte and continue
                    buffer.RemoveAt(0);
                    continue;
                }

                // Sanity check: packet size should be reasonable (max 64KB)
                if (packetSize > 65535)
                {
                    // Invalid packet size, remove the first byte and continue
                    buffer.RemoveAt(0);
                    continue;
                }

                // Check if we have the complete packet
                if (buffer.Count < packetSize)
                    break; // Wait for more data

                // Extract the complete packet
                var packet = new byte[packetSize];
                for (int i = 0; i < packetSize; i++)
                {
                    packet[i] = buffer[i];
                }

                // Remove the packet from the buffer
                buffer.RemoveRange(0, packetSize);

                packets.Add(packet);
            }

            return packets;
        }
        
        /// <summary>
        /// Gets the size of a packet from its header.
        /// C1 and C3 packets have a maximum length of 255, and the length defined in the second byte.
        /// C2 and C4 packets have a maximum length of 65535, and the length defined in the second and third byte.
        /// </summary>
        /// <param name="buffer">The buffer containing the packet header</param>
        /// <returns>The size of the packet, or 0 if invalid</returns>
        private static int GetPacketSize(List<byte> buffer)
        {
            if (buffer.Count < 2)
                return 0;
            
            switch (buffer[0])
            {
                case 0xC1:
                case 0xC3:
                    return buffer[1];
                case 0xC2:
                case 0xC4:
                    if (buffer.Count < 3)
                        return 0;
                    return (buffer[1] << 8) | buffer[2];
                default:
                    return 0; // Invalid packet type
            }
        }
    }
}
