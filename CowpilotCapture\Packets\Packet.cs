﻿using System;

namespace CowpilotCapture.Packets
{
    public class Packet
    {
        private readonly byte[] data;
        private readonly PacketType type;
        private readonly int length;
        private readonly byte code;

        public byte[] Data => data;

        public PacketType Type => type;

        public int Length => length;

        public byte Code => code;

        public Packet(byte[] data)
        {
            this.data = data;
            this.type = (PacketType)data[0];

            if (this.type == PacketType.C1 || this.type == PacketType.C3)
            {
                this.length = data[1];
                this.code = data[2];
            }
            else
            {
                this.length = data[1] << 8 | data[2];
                this.code = data[3];
            }
        }
    }

    public enum PacketType : byte
    {
        C1 = 0xC1,
        C2 = 0xC2,
        C3 = 0xC3,
        C4 = 0xC4,
    }
}
