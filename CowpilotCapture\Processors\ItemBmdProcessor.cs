using System;
using System.Collections.Generic;
using System.Text;

namespace CowpilotCapture.Processors
{
    /// <summary>
    /// Processes decrypted Item.bmd file data into a dictionary mapping item IDs to item names.
    /// Each entry in the BMD file is 84 bytes long, with the item name stored in the first 32 bytes (null-terminated).
    /// The item ID is the index/position of each entry in the file.
    /// </summary>
    public class ItemBmdProcessor
    {
        private const int EntrySize = 84;
        private const int NameSize = 32;

        /// <summary>
        /// Processes the decrypted Item.bmd file data and returns a dictionary of item ID to item name.
        /// </summary>
        /// <param name="decryptedData">The decrypted BMD file data</param>
        /// <returns>Dictionary mapping item ID (index) to item name</returns>
        /// <exception cref="ArgumentNullException">Thrown when decryptedData is null</exception>
        /// <exception cref="ArgumentException">Thrown when data length is not a multiple of entry size</exception>
        public Dictionary<int, string> ProcessItemData(byte[] decryptedData)
        {
            if (decryptedData == null)
                throw new ArgumentNullException(nameof(decryptedData));

            var itemDictionary = new Dictionary<int, string>();
            int entryCount = decryptedData.Length / EntrySize;

            for (int i = 0; i < entryCount; i++)
            {
                int itemId = i;
                int entryOffset = i * EntrySize;

                // Extract the name from the first 32 bytes of the entry
                string itemName = ExtractItemName(decryptedData, entryOffset);

                // Only add entries with non-empty names
                if (!string.IsNullOrEmpty(itemName))
                {
                    itemDictionary[itemId] = itemName;
                }
            }

            return itemDictionary;
        }

        /// <summary>
        /// Extracts the item name from the specified offset in the data.
        /// The name is stored in the first 32 bytes and is null-terminated.
        /// </summary>
        /// <param name="data">The BMD file data</param>
        /// <param name="offset">The offset to start reading from</param>
        /// <returns>The extracted item name, or empty string if no valid name found</returns>
        private string ExtractItemName(byte[] data, int offset)
        {
            // Find the null terminator within the first 32 bytes
            int nameLength = 0;
            for (int i = 0; i < NameSize && (offset + i) < data.Length; i++)
            {
                if (data[offset + i] == 0)
                {
                    nameLength = i;
                    break;
                }
                nameLength = i + 1;
            }

            // If no characters found, return empty string
            if (nameLength == 0)
                return string.Empty;

            // Convert bytes to string using ASCII encoding (common for game data)
            try
            {
                return Encoding.ASCII.GetString(data, offset, nameLength).Trim();
            }
            catch (Exception)
            {
                // If conversion fails, return empty string
                return string.Empty;
            }
        }

        /// <summary>
        /// Convenience method that combines BMD decryption and processing.
        /// </summary>
        /// <param name="filePath">Path to the encrypted Item.bmd file</param>
        /// <param name="xorKey">XOR key for decryption</param>
        /// <returns>Dictionary mapping item ID to item name</returns>
        public Dictionary<int, string> ProcessItemBmdFile(string filePath, byte[] xorKey)
        {
            var decryptor = new Encryption.BmdDecryptor(xorKey);
            var decryptedData = decryptor.DecryptBmdFile(filePath);
            return ProcessItemData(decryptedData);
        }
    }
}
