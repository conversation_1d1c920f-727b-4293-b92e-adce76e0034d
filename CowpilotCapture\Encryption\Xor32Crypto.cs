using System;

namespace CowpilotCapture.Encryption
{
    /// <summary>
    /// Provides XOR32 encryption and decryption functionality for Mu Online packets.
    /// </summary>
    public static class Xor32Crypto
    {
        /// <summary>
        /// The default 32 byte long XOR key for Pre-Season 6.
        /// </summary>
        public static readonly byte[] DefaultXor32Key =
        {
            0xE7,
            0x6D,
            0x3A,
            0x89,
            0xBC,
            0xB2,
            0x9F,
            0x73,
            0x23,
            0xA8,
            0xFE,
            0xB6,
            0x49,
            0x5D,
            0x39,
            0x5D,
            0x8A,
            0xCB,
            0x63,
            0x8D,
            0xEA,
            0x7D,
            0x2B,
            0x5F,
            0xC3,
            0xB1,
            0xE9,
            0x83,
            0x29,
            0x51,
            0xE8,
            0x56,
        };

        /// <summary>
        /// Encrypts a packet using XOR32 encryption.
        /// </summary>
        /// <param name="packet">The packet to encrypt.</param>
        /// <param name="xor32Key">The 32-byte XOR key. If null, uses the default key.</param>
        /// <returns>The encrypted packet.</returns>
        public static byte[] Encrypt(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (xor32Key == null)
                xor32Key = DefaultXor32Key;

            if (xor32Key.Length != 32)
                throw new ArgumentException(
                    "XOR32 key must be exactly 32 bytes long.",
                    nameof(xor32Key)
                );

            var result = new byte[packet.Length];
            Array.Copy(packet, result, packet.Length);

            var headerSize = result.GetPacketHeaderSize();

            // XOR32 encryption: each byte is XORed with the previous byte and the key
            for (int i = headerSize + 1; i < packet.Length; i++)
            {
                result[i] = (byte)(result[i] ^ result[i - 1] ^ xor32Key[i % 32]);
            }

            return result;
        }

        /// <summary>
        /// Decrypts a packet using XOR32 decryption.
        /// </summary>
        /// <param name="packet">The packet to decrypt.</param>
        /// <param name="xor32Key">The 32-byte XOR key. If null, uses the default key.</param>
        /// <returns>The decrypted packet.</returns>
        public static byte[] Decrypt(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (xor32Key == null)
                xor32Key = DefaultXor32Key;

            if (xor32Key.Length != 32)
                throw new ArgumentException(
                    "XOR32 key must be exactly 32 bytes long.",
                    nameof(xor32Key)
                );

            var result = new byte[packet.Length];
            Array.Copy(packet, result, packet.Length);

            var headerSize = result.GetPacketHeaderSize();

            // XOR32 decryption: reverse order, each byte is XORed with the previous byte and the key
            for (var i = result.Length - 1; i > headerSize; i--)
            {
                result[i] = (byte)(result[i] ^ result[i - 1] ^ xor32Key[i % 32]);
            }

            return result;
        }

        /// <summary>
        /// Encrypts a packet in place using XOR32 encryption.
        /// </summary>
        /// <param name="packet">The packet to encrypt in place.</param>
        /// <param name="xor32Key">The 32-byte XOR key. If null, uses the default key.</param>
        public static void EncryptInPlace(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (xor32Key == null)
                xor32Key = DefaultXor32Key;

            if (xor32Key.Length != 32)
                throw new ArgumentException(
                    "XOR32 key must be exactly 32 bytes long.",
                    nameof(xor32Key)
                );

            var headerSize = packet.GetPacketHeaderSize();

            // XOR32 encryption: each byte is XORed with the previous byte and the key
            for (int i = headerSize + 1; i < packet.Length; i++)
            {
                packet[i] = (byte)(packet[i] ^ packet[i - 1] ^ xor32Key[i % 32]);
            }
        }

        /// <summary>
        /// Decrypts a packet in place using XOR32 decryption.
        /// </summary>
        /// <param name="packet">The packet to decrypt in place.</param>
        /// <param name="xor32Key">The 32-byte XOR key. If null, uses the default key.</param>
        public static void DecryptInPlace(byte[] packet, byte[] xor32Key = null)
        {
            if (packet == null)
                throw new ArgumentNullException(nameof(packet));

            if (xor32Key == null)
                xor32Key = DefaultXor32Key;

            if (xor32Key.Length != 32)
                throw new ArgumentException(
                    "XOR32 key must be exactly 32 bytes long.",
                    nameof(xor32Key)
                );

            var headerSize = packet.GetPacketHeaderSize();

            // XOR32 decryption: reverse order, each byte is XORed with the previous byte and the key
            for (var i = packet.Length - 1; i > headerSize; i--)
            {
                packet[i] = (byte)(packet[i] ^ packet[i - 1] ^ xor32Key[i % 32]);
            }
        }
    }
}
