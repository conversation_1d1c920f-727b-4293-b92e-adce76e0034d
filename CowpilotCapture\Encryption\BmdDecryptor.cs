﻿using System.IO;

namespace CowpilotCapture.Encryption
{
    public class BmdDecryptor
    {
        private readonly byte[] xorKey;

        public BmdDecryptor(byte[] xorKey)
        {
            this.xorKey = xorKey;
        }

        public byte[] DecryptBmd(byte[] encryptedData)
        {
            if (encryptedData == null || encryptedData.Length == 0)
                return encryptedData;

            byte[] decryptedData = new byte[encryptedData.Length];

            for (int i = 0; i < encryptedData.Length; i++)
            {
                decryptedData[i] = (byte)(encryptedData[i] ^ xorKey[i % xorKey.Length]);
            }

            return decryptedData;
        }

        public byte[] DecryptBmdFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"BMD file not found: {filePath}");
            }

            byte[] encryptedData = File.ReadAllBytes(filePath);
            byte[] decryptedData = DecryptBmd(encryptedData);

            return decryptedData;
        }
    }
}
