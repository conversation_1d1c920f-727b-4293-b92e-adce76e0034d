namespace CowpilotCapture.Encryption
{
    /// <summary>
    /// Number conversion extensions.
    /// </summary>
    public static class NumberConversionExtensions
    {
        /// <summary>
        /// Converts 4 bytes to an 32bit unsigned Integer.
        /// </summary>
        /// <param name="lowest">The lowest.</param>
        /// <param name="lower">The lower.</param>
        /// <param name="higher">The higher.</param>
        /// <param name="highest">The highest.</param>
        /// <returns>
        /// The unsigned integer.
        /// </returns>
        public static uint MakeDword(byte lowest, byte lower, byte higher, byte highest)
        {
            return (uint)(MakeWord(lowest, lower) + (MakeWord(higher, highest) << 0x10));
        }

        /// <summary>
        /// Converts 2 bytes to one 16bit unsigned short.
        /// </summary>
        /// <param name="lowByte">The low byte.</param>
        /// <param name="highByte">The high byte.</param>
        /// <returns>The unsigned short.</returns>
        public static ushort MakeWord(byte lowByte, byte highByte)
        {
            return (ushort)(lowByte + ((highByte << 8) & 0xFF00));
        }

        /// <summary>
        /// Gets the high byte.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <returns>The high byte.</returns>
        public static byte GetHighByte(this ushort value)
        {
            return (byte)(value >> 8 & 0xFF);
        }

        /// <summary>
        /// Gets the low byte.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <returns>The low byte.</returns>
        public static byte GetLowByte(this ushort value)
        {
            return (byte)(value & 0xFF);
        }

        /// <summary>
        /// Gets the byte of an integer at a specific index position.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <param name="index">The index.</param>
        /// <returns>The byte of the integer at the specific index position.</returns>
        public static byte GetByte(this uint value, int index)
        {
            return (byte)((value >> (8 * index)) & 0xFF);
        }
    }
}
